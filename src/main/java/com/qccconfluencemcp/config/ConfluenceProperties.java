package com.qccconfluencemcp.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Confluence连接配置属性
 */
@Component
@ConfigurationProperties(prefix = "confluence")
public class ConfluenceProperties {
    
    /**
     * Confluence服务器URL
     */
    private String baseUrl = "https://doc.greatld.com";
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码或API Token
     */
    private String password;
    
    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;
    
    /**
     * 是否启用SSL验证
     */
    private boolean sslVerification = true;

    // Getters and Setters
    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }

    public boolean isSslVerification() {
        return sslVerification;
    }

    public void setSslVerification(boolean sslVerification) {
        this.sslVerification = sslVerification;
    }
}
