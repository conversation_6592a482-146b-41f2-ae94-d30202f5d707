# Confluence MCP服务器配置
spring:
  application:
    name: confluence-mcp-server

  # Spring AI MCP服务器配置
  ai:
    mcp:
      server:
        enabled: true
        name: confluence-mcp-server
        version: 1.0.0
        type: SYNC
        # 支持STDIO和SSE两种传输模式
        stdio: false  # 默认使用SSE，可通过命令行参数启用STDIO
        sse-message-endpoint: /mcp/message
        # 变更通知
        resource-change-notification: true
        tool-change-notification: true
        prompt-change-notification: true

# Confluence连接配置
confluence:
  # Confluence服务器地址
  base-url: https://doc.greatld.com

  # 认证信息（请在环境变量或外部配置文件中设置）
  username: ${CONFLUENCE_USERNAME:}
  password: ${CONFLUENCE_PASSWORD:}

  # 连接超时配置
  connect-timeout: 30000
  read-timeout: 60000

  # SSL验证
  ssl-verification: true

# 日志配置
logging:
  level:
    com.qccconfluencemcp: INFO
    org.springframework.ai: INFO
    org.springframework.ai.mcp: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  # STDIO模式需要的日志配置
  main:
    banner-mode: off
  file:
    name: ./target/confluence-mcp-server.log
