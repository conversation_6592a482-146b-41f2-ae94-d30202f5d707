# Confluence MCP服务器配置
spring:
  application:
    name: confluence-mcp-server

  # 禁用Web应用类型（STDIO模式需要）
  main:
    web-application-type: none
    banner-mode: off

  # Spring AI MCP服务器配置
  ai:
    mcp:
      server:
        enabled: true
        name: confluence-mcp-server
        version: 1.0.0
        type: SYNC
        # 变更通知
        resource-change-notification: true
        tool-change-notification: true
        prompt-change-notification: true

# Confluence连接配置
confluence:
  # Confluence服务器地址
  base-url: https://doc.greatld.com

  # 认证信息（请在环境变量或外部配置文件中设置）
  username: ${CONFLUENCE_USERNAME:}
  password: ${CONFLUENCE_PASSWORD:}

  # 连接超时配置
  connect-timeout: 30000
  read-timeout: 60000

  # SSL验证
  ssl-verification: true

# 日志配置
logging:
  level:
    com.qccconfluencemcp: INFO
    org.springframework.ai: INFO
    org.springframework.ai.mcp: DEBUG
  # STDIO模式需要清空控制台日志格式
  pattern:
    console: ""
  file:
    name: ./target/confluence-mcp-server.log
