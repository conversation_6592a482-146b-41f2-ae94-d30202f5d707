# Confluence MCP服务器

这是一个基于Java MCP SDK实现的Confluence内容读取MCP（Model Context Protocol）服务器，可以让AI模型通过标准化接口访问Confluence页面内容。

## 功能特性

- **页面内容读取**: 通过页面ID或URL获取Confluence页面的完整内容
- **内容搜索**: 在Confluence中搜索页面内容
- **标准MCP协议**: 实现完整的MCP服务器功能
- **灵活配置**: 支持多种Confluence实例配置

## 技术架构

- **Java MCP SDK**: 使用官方Java MCP SDK实现
- **Spring Boot**: 应用框架
- **WebFlux**: 响应式HTTP客户端
- **STDIO传输**: 通过标准输入输出与客户端通信

## 快速开始

### 1. 环境要求

- Java 17+
- Maven 3.6+
- Confluence服务器访问权限

### 2. 配置

在运行前需要设置Confluence连接信息：

```bash
# 设置环境变量
export CONFLUENCE_USERNAME=your_username
export CONFLUENCE_PASSWORD=your_password_or_api_token
```

或者修改 `src/main/resources/application.yml` 文件：

```yaml
confluence:
  base-url: https://your-confluence-instance.com
  username: your_username
  password: your_password_or_api_token
```

### 3. 构建和运行

```bash
# 构建项目
./mvnw clean package

# 运行服务器
./mvnw spring-boot:run
```

## MCP工具

服务器提供以下MCP工具：

### 1. get_confluence_page

获取Confluence页面的完整内容。

**参数**:
- `pageId` (必需): Confluence页面ID
- `pageUrl` (可选): Confluence页面URL

**示例**:
```json
{
  "pageId": "63680041"
}
```

或

```json
{
  "pageUrl": "https://doc.greatld.com/pages/viewpage.action?pageId=63680041"
}
```

### 2. search_confluence

在Confluence中搜索页面内容。

**参数**:
- `query` (必需): 搜索关键词
- `limit` (可选): 返回结果数量限制（默认10，最大50）
- `start` (可选): 结果起始位置（默认0）

**示例**:
```json
{
  "query": "API文档",
  "limit": 20,
  "start": 0
}
```

## 客户端集成

### 使用MCP客户端连接

```bash
# 通过STDIO连接到服务器
java -jar target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar
```

### 在AI应用中使用

可以将此MCP服务器集成到支持MCP协议的AI应用中，如Claude Desktop、VS Code扩展等。

## 配置选项

### Confluence配置

```yaml
confluence:
  base-url: https://your-confluence-instance.com  # Confluence服务器地址
  username: your_username                          # 用户名
  password: your_password                          # 密码或API Token
  connect-timeout: 30000                          # 连接超时（毫秒）
  read-timeout: 60000                             # 读取超时（毫秒）
  ssl-verification: true                          # 是否启用SSL验证
```

### 日志配置

```yaml
logging:
  level:
    com.qccconfluencemcp: INFO
    io.modelcontextprotocol: DEBUG
```

## 安全注意事项

1. **认证信息保护**: 不要在代码中硬编码用户名和密码，使用环境变量或安全的配置管理
2. **网络安全**: 确保Confluence服务器的网络访问安全
3. **权限控制**: 使用具有适当权限的Confluence账户

## 故障排除

### 常见问题

1. **连接失败**: 检查Confluence服务器地址和网络连接
2. **认证失败**: 验证用户名和密码是否正确
3. **页面访问失败**: 确认用户有访问指定页面的权限

### 日志调试

启用调试日志：

```yaml
logging:
  level:
    com.qccconfluencemcp: DEBUG
    io.modelcontextprotocol: DEBUG
```

## 开发

### 项目结构

```
src/main/java/com/qccconfluencemcp/
├── config/                 # 配置类
│   └── ConfluenceProperties.java
├── model/                  # 数据模型
│   ├── ConfluencePage.java
│   └── ConfluenceSearchResult.java
├── service/                # 服务层
│   └── ConfluenceClient.java
├── mcp/                    # MCP相关
│   ├── ConfluenceTools.java
│   └── ConfluenceMcpServer.java
└── QccConfluenceMcpApplication.java
```

### 扩展功能

可以通过以下方式扩展功能：

1. 在 `ConfluenceTools` 中添加新的工具
2. 在 `ConfluenceClient` 中添加新的API调用
3. 添加新的数据模型支持更多Confluence功能

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
